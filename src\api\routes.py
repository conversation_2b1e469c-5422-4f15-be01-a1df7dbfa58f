"""
API Routes for Recipe Recommender

This module defines the API routes for the recipe recommendation system.
"""

from flask import jsonify, request, render_template, redirect, url_for, current_app, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt

# Create a blueprint for the main routes
main_bp = Blueprint('main', __name__)
from api.decorators import login_required
from api.models.recipe import (
    get_recipe_by_id,
    get_recipe_by_original_id,
    save_recipe_to_db,
    get_saved_recipes_for_user,
    save_recipe_for_user,
    remove_saved_recipe_for_user
)
from api.models.user import (
    save_search_history,
    get_dashboard_data,
    clear_search_history,
    remove_search_from_history,
    update_user_analytics,
    get_user_analytics
)
from api.models.community import (
    add_recipe_review,
    get_recipe_reviews,
    get_recipe_rating_summary,
    vote_on_review,
    add_recipe_verification,
    get_recipe_verifications,
    get_verification_photo,
    get_user_review_for_recipe,
    get_user_verification_for_recipe,
    create_community_indexes
)

# Import ingredient filter for analytics
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Try to import ingredient_filter, with fallback if not found
try:
    from ingredient_filter import filter_ingredients_list, is_main_ingredient
except ImportError:
    # Fallback functions if ingredient_filter is not available
    def filter_ingredients_list(ingredients):
        return ingredients

    def is_main_ingredient(ingredient):
        return True

def _simple_fuzzy_match(str1, str2, threshold=0.6):
    """Simple fuzzy matching for ingredient names."""
    if not str1 or not str2:
        return False

    # Check for common variations (plural/singular)
    variations = [
        (str1, str2),
        (str1.rstrip('s'), str2),
        (str1, str2.rstrip('s')),
        (str1.rstrip('es'), str2),
        (str1, str2.rstrip('es'))
    ]

    for v1, v2 in variations:
        if v1 == v2:
            return True

    # Simple similarity check
    longer = str1 if len(str1) > len(str2) else str2
    shorter = str2 if len(str1) > len(str2) else str1

    if len(longer) == 0:
        return True

    # Count matching characters
    matches = sum(1 for c in shorter if c in longer)
    similarity = matches / len(longer)

    return similarity >= threshold

@main_bp.route('/', methods=['GET'])
def home():
    """Home page for the recipe recommendation system."""
    return render_template('home.html')

@main_bp.route('/welcome', methods=['GET'])
def welcome():
    """Welcome page with analytics and recipe information."""
    return render_template('welcome.html')

@main_bp.route('/login', methods=['GET'])
def login():
    """Login page for the recipe recommendation system."""
    return render_template('login.html')

@main_bp.route('/signup', methods=['GET'])
def signup():
    """Sign up page for the recipe recommendation system."""
    return render_template('signup.html')

@main_bp.route('/dashboard', methods=['GET'])
def dashboard():
    """Dashboard page for the recipe recommendation system."""
    # We'll handle authentication in the client-side JavaScript
    return render_template('dashboard.html')

@main_bp.route('/save-recipe', methods=['GET'])
def save_recipe_page():
    """Saved recipes page for the recipe recommendation system."""
    # We'll handle authentication in the client-side JavaScript
    return render_template('save-recipe.html')

@main_bp.route('/profile', methods=['GET'])
def profile_page():
    """Profile page for the recipe recommendation system."""
    # We'll handle authentication in the client-side JavaScript
    return render_template('profile.html')

@main_bp.route('/search-results', methods=['GET'])
def search_results_page():
    """Search results page for displaying all recommended recipes."""
    # We'll handle authentication in the client-side JavaScript
    return render_template('search-results.html')

@main_bp.route('/admin', methods=['GET'])
def admin_page():
    """Admin page for viewing all user information."""
    return render_template('admin.html')

@main_bp.route('/forgot-password', methods=['GET'])
def forgot_password_page():
    """Forgot password page for password reset requests."""
    return render_template('forgot-password.html')

@main_bp.route('/reset-password', methods=['GET'])
def reset_password_page():
    """Reset password page for setting new password with token."""
    return render_template('reset-password.html')



@main_bp.route('/api/docs', methods=['GET'])
def api_docs():
    """API documentation page."""
    return render_template('api_docs.html')

@main_bp.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    # Get recommender from current_app
    recommender = getattr(current_app, 'recommender', None)
    if recommender is None:
        return jsonify({
            'status': 'error',
            'message': 'Recommender not initialized'
        }), 500

    return jsonify({
        'status': 'ok',
        'message': 'API is running',
        'recipes_loaded': len(recommender.recipes),
        'ingredients_loaded': len(recommender.ingredient_names)
    })

@main_bp.route('/api/ingredients', methods=['GET'])
def get_ingredients():
    """
    Get all available ingredients.

    Query Parameters:
    ----------------
    search : str, optional
        Search term to filter ingredients by name
    limit : int, optional
        Maximum number of ingredients to return (default: 100)
    """
    # Get recommender from current_app
    recommender = getattr(current_app, 'recommender', None)
    if recommender is None:
        return jsonify({
            'status': 'error',
            'message': 'Recommender not initialized'
        }), 500

    # Get query parameters
    search = request.args.get('search', '').lower()
    limit = int(request.args.get('limit', 100))

    # Filter ingredients by search term
    ingredients = []
    count = 0

    for ingredient_name in recommender.ingredient_names:
        if count >= limit:
            break

        if search and search not in str(ingredient_name).lower():
            continue

        ingredients.append({
            'id': ingredient_name,  # Use the name as ID for clean recipes
            'name': ingredient_name
        })
        count += 1

    return jsonify({
        'status': 'ok',
        'count': len(ingredients),
        'ingredients': ingredients
    })

@main_bp.route('/api/recommend', methods=['POST'])
def recommend_recipes():
    """
    Recommend recipes based on ingredients.

    Request Body:
    ------------
    {
        "ingredients": ["egg", "chicken", "rice"],
        "limit": 10,
        "min_score": 0.05,
        "strict": false
    }

    Returns:
    --------
    {
        "status": "ok",
        "count": 5,
        "recipes": [
            {
                "id": "123",
                "name": "Chicken Fried Rice",
                "ingredients": ["egg", "chicken", "rice", "soy sauce", "onion"],
                "steps": ["Step 1...", "Step 2..."],
                "techniques": ["frying", "boiling"],
                "calorie_level": 1,
                "score": 0.85,
                "ingredient_match_percentage": 75.0
            },
            ...
        ]
    }
    """
    # Get recommender from current_app
    recommender = getattr(current_app, 'recommender', None)
    if recommender is None:
        return jsonify({
            'status': 'error',
            'message': 'Recommender not initialized'
        }), 500

    # Get request body
    data = request.get_json()

    if not data or 'ingredients' not in data:
        return jsonify({
            'status': 'error',
            'message': 'Missing required parameter: ingredients'
        }), 400

    # Get parameters
    ingredients = data.get('ingredients', [])
    limit = int(data.get('limit', 10))
    min_score = float(data.get('min_score', 0.05))
    strict = data.get('strict', False)

    if not ingredients:
        return jsonify({
            'status': 'error',
            'message': 'No ingredients provided'
        }), 400

    try:
        # Create ingredient string from the list
        ingredient_string = ', '.join(ingredients)

        # Get user ID if authenticated (optional for hybrid recommendations)
        user_id = None
        user_preferences = None
        try:
            user_id = get_jwt_identity()
            # You could load user preferences from database here if needed
            # user_preferences = get_user_preferences(user_id)
        except:
            pass  # User not authenticated, continue with anonymous recommendations

        # Get recommendations using the hybrid recommender
        recommendations = recommender.recommend_recipes(
            user_input=ingredient_string,
            user_id=user_id,
            user_preferences=user_preferences,
            num_recommendations=limit,
            explanation=True  # Include explanation for debugging
        )

        if not recommendations:
            return jsonify({
                'status': 'ok',
                'count': 0,
                'recipes': []
            })

        # Format the response
        recipes = []
        total_user_ingredients = len(ingredients)

        for recipe in recommendations:
            # Get matched ingredients from the recipe (provided by the recommender)
            matched_ingredients = recipe.get('matched_ingredients', [])

            # If matched_ingredients is empty, calculate it manually
            if not matched_ingredients:
                matched_ingredients = []
                recipe_ingredients = [ing.lower().strip() for ing in recipe['ingredients']]

                for user_ing in ingredients:
                    user_ing_lower = user_ing.lower().strip()
                    for recipe_ing in recipe_ingredients:
                        # Simple matching: check if ingredients are similar
                        if (user_ing_lower in recipe_ing or recipe_ing in user_ing_lower):
                            matched_ingredients.append(user_ing)
                            break

            # Calculate ingredient match percentage correctly
            matched_user_ingredients = len(matched_ingredients)
            match_percentage = (matched_user_ingredients / total_user_ingredients * 100) if total_user_ingredients > 0 else 0

            # Filter out recipes with 0% match at the backend level
            if match_percentage <= 0:
                continue

            # Determine if values are estimated (using default values)
            prep_time = recipe.get('prep_time', 30)
            cook_time = recipe.get('cook_time', 45)
            servings = recipe.get('servings', 4)
            cuisine = recipe.get('cuisine', 'International')
            difficulty = recipe.get('difficulty', 'Medium')

            # Check if values are defaults (indicating estimation)
            prep_time_estimated = prep_time == 30
            cook_time_estimated = cook_time == 45
            servings_estimated = servings == 4
            cuisine_estimated = cuisine == 'International'
            difficulty_estimated = difficulty == 'Medium'

            # Get rating data for this recipe
            rating_summary = get_recipe_rating_summary(str(recipe['id']))
            rating_data = None
            verification_data = None

            if rating_summary['status'] == 'success':
                rating_data = {
                    'average_rating': rating_summary['average_rating'],
                    'total_reviews': rating_summary['total_reviews'],
                    'rating_distribution': rating_summary['rating_distribution']
                }

            # Get verification data for this recipe
            from api.models.community import mongo
            verification_count = mongo.db.recipe_verifications.count_documents({'recipe_id': str(recipe['id'])})
            if verification_count > 0:
                verification_data = {
                    'verification_count': verification_count
                }

            # Prepare recipe data
            recipe_data = {
                'id': recipe['id'],
                'name': recipe['name'],
                'ingredients': recipe['ingredients'],
                'matched_ingredients': matched_ingredients,  # Add matched ingredients to response
                'steps': recipe.get('instructions', []),
                'techniques': [],  # Clean recipes don't have techniques field
                'calorie_level': 1,  # Default calorie level for clean recipes
                'score': recipe.get('score', recipe.get('hybrid_score', 0.5)),  # Use hybrid score if available
                'hybrid_score': recipe.get('hybrid_score', 0.5),
                'ingredient_match_percentage': round(match_percentage, 1),
                'prep_time': prep_time,
                'prep_time_estimated': prep_time_estimated,
                'cook_time': cook_time,
                'cook_time_estimated': cook_time_estimated,
                'servings': servings,
                'servings_estimated': servings_estimated,
                'cuisine': cuisine,
                'cuisine_estimated': cuisine_estimated,
                'difficulty': difficulty,
                'difficulty_estimated': difficulty_estimated,
                'rating_data': rating_data,
                'verification_data': verification_data
            }

            # Add recommendation explanation if available (for debugging)
            if 'recommendation_explanation' in recipe:
                recipe_data['recommendation_explanation'] = recipe['recommendation_explanation']

            recipes.append(recipe_data)

        # Sort recipes by hybrid score first (highest first), then by ingredient match percentage
        recipes.sort(key=lambda x: (x['hybrid_score'], x['ingredient_match_percentage']), reverse=True)

        return jsonify({
            'status': 'ok',
            'count': len(recipes),
            'recipes': recipes
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error recommending recipes: {str(e)}'
        }), 500

@main_bp.route('/api/recipe/<recipe_id>', methods=['GET'])
def get_recipe(recipe_id):
    """
    Get details for a specific recipe.

    Parameters:
    -----------
    recipe_id : str
        ID of the recipe to get
    """
    # Get recommender from current_app
    recommender = getattr(current_app, 'recommender', None)
    if recommender is None:
        return jsonify({
            'status': 'error',
            'message': 'Recommender not initialized'
        }), 500

    try:
        # Find the recipe by ID
        recipe = None
        for r in recommender.recipes:
            if str(r['id']) == recipe_id:
                recipe = r
                break

        if recipe is None:
            return jsonify({
                'status': 'error',
                'message': f'Recipe not found: {recipe_id}'
            }), 404

        # Format the response for clean recipes
        prep_time = recipe.get('prep_time', 30)
        cook_time = recipe.get('cook_time', 45)
        servings = recipe.get('servings', 4)
        cuisine = recipe.get('cuisine', 'International')
        difficulty = recipe.get('difficulty', 'Medium')

        # Get rating data for this recipe
        rating_summary = get_recipe_rating_summary(str(recipe['id']))
        rating_data = None
        verification_data = None

        if rating_summary['status'] == 'success':
            rating_data = {
                'average_rating': rating_summary['average_rating'],
                'total_reviews': rating_summary['total_reviews'],
                'rating_distribution': rating_summary['rating_distribution']
            }

        # Get verification data for this recipe
        from api.models.community import mongo
        verification_count = mongo.db.recipe_verifications.count_documents({'recipe_id': str(recipe['id'])})
        if verification_count > 0:
            verification_data = {
                'verification_count': verification_count
            }

        recipe_data = {
            'id': recipe['id'],
            'name': recipe['name'],
            'ingredients': recipe['ingredients'],
            'steps': recipe.get('instructions', []),
            'techniques': [],  # Clean recipes don't have techniques
            'calorie_level': 1,  # Default for clean recipes
            'prep_time': prep_time,
            'prep_time_estimated': prep_time == 30,
            'cook_time': cook_time,
            'cook_time_estimated': cook_time == 45,
            'servings': servings,
            'servings_estimated': servings == 4,
            'cuisine': cuisine,
            'cuisine_estimated': cuisine == 'International',
            'difficulty': difficulty,
            'difficulty_estimated': difficulty == 'Medium',
            'rating_data': rating_data,
            'verification_data': verification_data
        }

        return jsonify({
            'status': 'ok',
            'recipe': recipe_data
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error getting recipe: {str(e)}'
        }), 500

@main_bp.route('/api/recipe/<recipe_id>/save', methods=['POST'])
@jwt_required()
def save_recipe_api(recipe_id):
    """
    Save a recipe to the user's saved recipes.

    Parameters:
    -----------
    recipe_id : str
        ID of the recipe to save
    """
    # Get recommender from current_app
    recommender = getattr(current_app, 'recommender', None)
    if recommender is None:
        return jsonify({
            'status': 'error',
            'message': 'Recommender not initialized'
        }), 500

    try:
        # Get user ID from JWT
        user_id = get_jwt_identity()

        # Find the recipe by ID
        recipe = None
        for r in recommender.recipes:
            if str(r['id']) == recipe_id:
                recipe = r
                break

        if recipe is None:
            return jsonify({
                'status': 'error',
                'message': f'Recipe not found: {recipe_id}'
            }), 404

        # Format the recipe data for saving (clean recipes)
        recipe_data = {
            'original_id': recipe['id'],
            'name': recipe['name'],
            'ingredients': recipe['ingredients'],
            'steps': recipe.get('instructions', []),
            'techniques': [],  # Clean recipes don't have techniques
            'calorie_level': 1,  # Default for clean recipes
            'prep_time': recipe.get('prep_time', 30),
            'cook_time': recipe.get('cook_time', 45),
            'servings': recipe.get('servings', 4),
            'cuisine': recipe.get('cuisine', 'International'),
            'difficulty': recipe.get('difficulty', 'Medium')
        }

        # Save the recipe for the user
        success = save_recipe_for_user(user_id, recipe_data)

        if success:
            # Track analytics for recipe save
            try:
                update_user_analytics(user_id, 'recipe_save', {
                    'recipe_id': recipe_id,
                    'recipe_name': recipe['name']
                })
                print(f"Analytics tracked for recipe save: user={user_id}, recipe={recipe_id}")
            except Exception as e:
                print(f"Warning: Could not track recipe save analytics: {e}")

            return jsonify({
                'status': 'success',
                'message': 'Recipe saved successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to save recipe'
            }), 500

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error saving recipe: {str(e)}'
        }), 500

@main_bp.route('/api/recipes/saved', methods=['GET'])
@jwt_required()
def get_saved_recipes_api():
    """
    Get all recipes saved by the current user.
    """
    try:
        # Get user ID from JWT
        user_id = get_jwt_identity()

        # Get saved recipes
        recipes = get_saved_recipes_for_user(user_id)

        # Format the response
        formatted_recipes = []
        for recipe in recipes:
            formatted_recipes.append({
                'id': str(recipe['_id']),
                'name': recipe['name'],
                'ingredients': recipe['ingredients'],
                'steps': recipe['steps'],
                'techniques': recipe['techniques'],
                'calorie_level': recipe['calorie_level'],
                'ingredient_match_percentage': 100  # Always 100% for saved recipes
            })

        return jsonify({
            'status': 'success',
            'count': len(formatted_recipes),
            'recipes': formatted_recipes
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error getting saved recipes: {str(e)}'
        }), 500

@main_bp.route('/api/recipe/<recipe_id>/unsave', methods=['POST'])
@jwt_required()
def unsave_recipe_api(recipe_id):
    """
    Remove a recipe from the user's saved recipes.

    Parameters:
    -----------
    recipe_id : str
        ID of the recipe to remove
    """
    try:
        # Get user ID from JWT
        user_id = get_jwt_identity()

        # Remove the recipe from the user's saved recipes
        success = remove_saved_recipe_for_user(user_id, recipe_id)

        if success:
            return jsonify({
                'status': 'success',
                'message': 'Recipe removed from saved recipes'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to remove recipe from saved recipes'
            }), 500

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error removing recipe from saved recipes: {str(e)}'
        }), 500

# Dashboard API endpoints
@main_bp.route('/api/dashboard/data', methods=['GET'])
@jwt_required()
def get_dashboard_data_api():
    """
    Get dashboard data for the current user.

    Returns:
    --------
    {
        "status": "success",
        "data": {
            "recent_searches": [...],
            "ingredient_history": [...],
            "search_stats": {...}
        }
    }
    """
    try:
        # Get user ID from JWT
        user_id = get_jwt_identity()

        # Get dashboard data
        dashboard_data = get_dashboard_data(user_id)

        if dashboard_data is None:
            return jsonify({
                'status': 'error',
                'message': 'User not found'
            }), 404

        return jsonify({
            'status': 'success',
            'data': dashboard_data
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error getting dashboard data: {str(e)}'
        }), 500

@main_bp.route('/api/dashboard/search-history', methods=['POST'])
@jwt_required()
def save_search_history_api():
    """
    Save a search to user's search history.

    Request Body:
    ------------
    {
        "ingredients": "egg, rice, chicken",
        "ingredientsList": ["egg", "rice", "chicken"],
        "title": "Search Results",
        "timestamp": "2024-01-01T12:00:00Z"
    }
    """
    try:
        # Get user ID from JWT
        user_id = get_jwt_identity()

        # Get request data
        data = request.get_json()

        if not data:
            return jsonify({
                'status': 'error',
                'message': 'No data provided'
            }), 400

        # Validate required fields
        if 'ingredientsList' not in data or not data['ingredientsList']:
            return jsonify({
                'status': 'error',
                'message': 'ingredientsList is required'
            }), 400

        # Debug logging
        print(f"🔍 DEBUG: Saving search history for user {user_id}")
        print(f"🔍 DEBUG: Search data: {data}")

        # Save search history
        success = save_search_history(user_id, data)

        print(f"🔍 DEBUG: Search history save success: {success}")

        if success:
            return jsonify({
                'status': 'success',
                'message': 'Search history saved'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to save search history'
            }), 500

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error saving search history: {str(e)}'
        }), 500

@main_bp.route('/api/dashboard/search-history/clear', methods=['POST'])
@jwt_required()
def clear_search_history_api():
    """
    Clear all search history for the current user.
    """
    try:
        # Get user ID from JWT
        user_id = get_jwt_identity()

        # Clear search history
        success = clear_search_history(user_id)

        if success:
            return jsonify({
                'status': 'success',
                'message': 'Search history cleared'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to clear search history'
            }), 500

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error clearing search history: {str(e)}'
        }), 500

@main_bp.route('/api/dashboard/search-history/<int:search_index>', methods=['DELETE'])
@jwt_required()
def remove_search_from_history_api(search_index):
    """
    Remove a specific search from user's search history.

    Parameters:
    -----------
    search_index : int
        Index of the search to remove
    """
    try:
        # Get user ID from JWT
        user_id = get_jwt_identity()

        # Remove search from history
        success = remove_search_from_history(user_id, search_index)

        if success:
            return jsonify({
                'status': 'success',
                'message': 'Search removed from history'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to remove search from history'
            }), 500

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error removing search from history: {str(e)}'
        }), 500

# User Analytics API endpoints
@main_bp.route('/api/analytics/personal', methods=['GET'])
@jwt_required()
def get_personal_analytics():
    """
    Get personal analytics for the current user.

    Returns:
    --------
    {
        "status": "success",
        "analytics": {
            "personal_stats": {...},
            "favorite_ingredients": {...},
            "cuisine_preferences": {...},
            "cooking_streak": {...},
            "monthly_activity": {...},
            "recent_searches": [...]
        }
    }
    """
    try:
        # Get user ID from JWT
        user_id = get_jwt_identity()

        # Get user analytics
        analytics = get_user_analytics(user_id)

        if analytics is None:
            return jsonify({
                'status': 'error',
                'message': 'User not found'
            }), 404

        return jsonify({
            'status': 'success',
            'analytics': analytics
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error getting personal analytics: {str(e)}'
        }), 500

@main_bp.route('/api/analytics/prescriptive', methods=['GET'])
def get_prescriptive_analytics():
    """
    Get prescriptive analytics data for the welcome/dashboard pages.

    Returns:
    --------
    {
        "status": "success",
        "data": {
            "trending_recipes": [...],
            "popular_recipes": [...],
            "leftover_solutions": {...},
            "user_specific": {...} // Only if authenticated
        }
    }
    """
    try:
        from datetime import datetime, timedelta
        from collections import defaultdict, Counter
        import random

        # Import with error handling
        try:
            from api.models.user import mongo
        except ImportError as e:
            print(f"Warning: Could not import mongo: {e}")
            mongo = None

        try:
            from bson.objectid import ObjectId
        except ImportError as e:
            print(f"Warning: Could not import ObjectId: {e}")
            ObjectId = None

        # Get user ID if authenticated (optional)
        user_id = None
        try:
            user_id = get_jwt_identity()
        except:
            pass  # User not authenticated

        # Get trending recipes (recipes with recent high engagement)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        seven_days_ago = datetime.utcnow() - timedelta(days=7)

        # Get recent reviews and verifications with error handling
        recent_reviews = []
        recent_verifications = []

        if mongo:
            try:
                recent_reviews = list(mongo.db.recipe_reviews.find({
                    'created_at': {'$gte': seven_days_ago}
                }))

                recent_verifications = list(mongo.db.recipe_verifications.find({
                    'created_at': {'$gte': seven_days_ago}
                }))
            except Exception as e:
                print(f"Warning: Could not fetch reviews/verifications: {e}")
                recent_reviews = []
                recent_verifications = []

        # Calculate trending scores
        trending_scores = defaultdict(float)

        # Score based on recent reviews
        for review in recent_reviews:
            recipe_id = review.get('recipe_id')
            rating = review.get('rating', 3)
            # Higher weight for recent reviews, bonus for high ratings
            trending_scores[recipe_id] += (rating / 5.0) * 2.0

        # Score based on recent verifications
        for verification in recent_verifications:
            recipe_id = verification.get('recipe_id')
            trending_scores[recipe_id] += 1.5

        # Get popular recipes (all-time high ratings and engagement)
        all_reviews = []
        all_verifications = []

        if mongo:
            try:
                all_reviews = list(mongo.db.recipe_reviews.find())
                all_verifications = list(mongo.db.recipe_verifications.find())
            except Exception as e:
                print(f"Warning: Could not fetch all reviews/verifications: {e}")
                all_reviews = []
                all_verifications = []

        # Calculate popularity scores with enhanced metrics
        recipe_ratings = defaultdict(list)
        recipe_verification_counts = defaultdict(int)
        recipe_saves = defaultdict(int)

        for review in all_reviews:
            recipe_id = review.get('recipe_id')
            rating = review.get('rating', 3)
            recipe_ratings[recipe_id].append(rating)

        for verification in all_verifications:
            recipe_id = verification.get('recipe_id')
            recipe_verification_counts[recipe_id] += 1

        # Get recipe saves data from users
        if mongo:
            try:
                saved_recipes = list(mongo.db.saved_recipes.find())
                for saved_recipe in saved_recipes:
                    recipe_name = saved_recipe.get('name', '')
                    # Try to match saved recipe name to recipe ID
                    if recommender and recommender.recipes:
                        matching_recipe = next((r for r in recommender.recipes if r['name'].lower() == recipe_name.lower()), None)
                        if matching_recipe:
                            recipe_saves[str(matching_recipe['id'])] += 1
            except Exception as e:
                print(f"Warning: Could not fetch saved recipes data: {e}")

        # Calculate average ratings and popularity scores
        popular_scores = {}
        for recipe_id, ratings in recipe_ratings.items():
            avg_rating = sum(ratings) / len(ratings)
            review_count = len(ratings)
            verification_count = recipe_verification_counts.get(recipe_id, 0)
            saves_count = recipe_saves.get(recipe_id, 0)

            # Enhanced popularity score: rating (40%), review count (25%), verifications (20%), saves (15%)
            popularity_score = (
                (avg_rating * 0.4) +
                (min(review_count, 20) * 0.25) +
                (min(verification_count, 10) * 0.2) +
                (min(saves_count, 15) * 0.15)
            )
            popular_scores[recipe_id] = {
                'score': popularity_score,
                'avg_rating': avg_rating,
                'review_count': review_count,
                'verification_count': verification_count,
                'saves': saves_count
            }

        # Get top trending and popular recipe IDs
        top_trending_ids = sorted(trending_scores.items(), key=lambda x: x[1], reverse=True)[:5]
        top_popular_ids = sorted(popular_scores.items(), key=lambda x: x[1]['score'], reverse=True)[:10]

        print(f"🔍 DEBUG: Found {len(popular_scores)} recipes with real ratings/reviews")
        print(f"🔍 DEBUG: Top popular recipe scores: {[(rid, data['score']) for rid, data in top_popular_ids[:3]]}")

        # Get recipe details from recommender
        trending_recipes = []
        popular_recipes = []

        # Get recommender from current_app
        recommender = getattr(current_app, 'recommender', None)
        if recommender and recommender.recipes:
            # Get trending recipes
            for recipe_id, score in top_trending_ids:
                recipe = next((r for r in recommender.recipes if str(r['id']) == str(recipe_id)), None)
                if recipe:
                    recipe_data = {
                        'id': recipe['id'],
                        'name': recipe['name'],
                        'ingredients': recipe['ingredients'][:5],  # First 5 ingredients
                        'description': f"Trending recipe with {score:.1f} engagement score",
                        'trending_score': score,
                        'prep_time': recipe.get('prep_time', 30),
                        'difficulty': recipe.get('difficulty', 'Medium')
                    }
                    trending_recipes.append(recipe_data)

            # Get popular recipes with enhanced data
            for recipe_id, data in top_popular_ids:
                recipe = next((r for r in recommender.recipes if str(r['id']) == str(recipe_id)), None)
                if recipe:
                    # Get latest review for this recipe
                    latest_review = None
                    if mongo:
                        try:
                            latest_review_doc = mongo.db.recipe_reviews.find_one(
                                {'recipe_id': str(recipe_id)},
                                sort=[('created_at', -1)]
                            )
                            if latest_review_doc:
                                latest_review = {
                                    'text': latest_review_doc.get('review_text', ''),
                                    'user_name': latest_review_doc.get('user_name', 'Anonymous'),
                                    'rating': latest_review_doc.get('rating', 5)
                                }
                        except Exception as e:
                            print(f"Warning: Could not fetch latest review for recipe {recipe_id}: {e}")

                    recipe_data = {
                        'id': recipe['id'],
                        'name': recipe['name'],
                        'ingredients': recipe['ingredients'][:5],  # First 5 ingredients
                        'description': f"Highly rated recipe ({data['avg_rating']:.1f}/5 stars) with {data['review_count']} reviews",
                        'avg_rating': data['avg_rating'],
                        'rating': data['avg_rating'],  # Alias for compatibility
                        'review_count': data['review_count'],
                        'total_reviews': data['review_count'],  # Alias for compatibility
                        'verification_count': data['verification_count'],
                        'prep_time': recipe.get('prep_time', 30),
                        'difficulty': recipe.get('difficulty', 'Medium'),
                        'saves': data.get('saves', 0),
                        'total_saves': data.get('saves', 0),  # Alias for compatibility
                        'latest_review': latest_review
                    }
                    popular_recipes.append(recipe_data)

        # Only use fallback data if we have insufficient real data
        print(f"🔍 DEBUG: Real popular recipes found: {len(popular_recipes)}")

        if len(popular_recipes) < 3:
            fallback_popular = [
                {
                    'id': 'popular-1',
                    'name': 'Classic Chicken Curry',
                    'ingredients': ['chicken', 'curry powder', 'coconut milk', 'onion', 'garlic'],
                    'description': 'Highly rated recipe (4.8/5 stars) with 156 reviews',
                    'avg_rating': 4.8,
                    'rating': 4.8,
                    'review_count': 156,
                    'total_reviews': 156,
                    'verification_count': 23,
                    'prep_time': 45,
                    'difficulty': 'Medium',
                    'saves': 234,
                    'total_saves': 234,
                    'latest_review': {
                        'text': 'Amazing flavor! Used leftover chicken and it was perfect.',
                        'user_name': 'Sarah K.',
                        'rating': 5
                    }
                },
                {
                    'id': 'popular-2',
                    'name': 'Perfect Pancakes',
                    'ingredients': ['flour', 'egg', 'milk', 'sugar', 'baking powder'],
                    'description': 'Community favorite (4.9/5 stars) with 203 reviews',
                    'avg_rating': 4.9,
                    'rating': 4.9,
                    'review_count': 203,
                    'total_reviews': 203,
                    'verification_count': 45,
                    'prep_time': 15,
                    'difficulty': 'Easy',
                    'saves': 189,
                    'total_saves': 189,
                    'latest_review': {
                        'text': 'Fluffy and delicious! Kids loved them.',
                        'user_name': 'Mike R.',
                        'rating': 5
                    }
                },
                {
                    'id': 'popular-3',
                    'name': 'Homemade Fried Rice',
                    'ingredients': ['rice', 'egg', 'soy sauce', 'vegetables', 'garlic'],
                    'description': 'Perfect for leftovers (4.6/5 stars) with 142 reviews',
                    'avg_rating': 4.6,
                    'rating': 4.6,
                    'review_count': 142,
                    'total_reviews': 142,
                    'verification_count': 31,
                    'prep_time': 20,
                    'difficulty': 'Easy',
                    'saves': 167,
                    'total_saves': 167,
                    'latest_review': {
                        'text': 'Great way to use leftover rice. So tasty!',
                        'user_name': 'Lisa T.',
                        'rating': 4
                    }
                }
            ]

            # Only add fallback recipes if we need more to reach 3 recipes
            needed_recipes = 3 - len(popular_recipes)
            if needed_recipes > 0:
                popular_recipes.extend(fallback_popular[:needed_recipes])
                print(f"🔍 DEBUG: Added {needed_recipes} fallback recipes to supplement real data")
        else:
            # We have enough real data, just take the top 3
            popular_recipes = popular_recipes[:3]
            print(f"🔍 DEBUG: Using {len(popular_recipes)} real popular recipes")

        # Get leftover solutions data
        leftover_solutions = {}

        # Get most frequently searched ingredients from all users
        all_users = []

        if mongo:
            try:
                all_users = list(mongo.db.users.find({}, {
                    'dashboard_data.search_stats.most_used_ingredients': 1,
                    'dashboard_data.ingredient_history': 1
                }))
            except Exception as e:
                print(f"Warning: Could not fetch user data: {e}")
                all_users = []

        # Aggregate ingredient usage across all users
        global_ingredient_usage = defaultdict(int)
        for user in all_users:
            dashboard_data = user.get('dashboard_data', {})
            search_stats = dashboard_data.get('search_stats', {})
            most_used = search_stats.get('most_used_ingredients', {})

            for ingredient, count in most_used.items():
                global_ingredient_usage[ingredient] += count

        # Get top leftover ingredients
        top_leftovers = sorted(global_ingredient_usage.items(), key=lambda x: x[1], reverse=True)[:10]

        # Common leftover combinations and their recipe suggestions
        leftover_combinations = {
            'rice_egg': {
                'ingredients': ['rice', 'egg'],
                'recipes': ['Fried Rice', 'Egg Rice Bowl', 'Rice Omelette'],
                'description': 'Perfect for using leftover rice'
            },
            'chicken_vegetables': {
                'ingredients': ['chicken', 'vegetables'],
                'recipes': ['Chicken Stir Fry', 'Chicken Soup', 'Chicken Salad'],
                'description': 'Great way to use leftover chicken'
            },
            'bread_milk': {
                'ingredients': ['bread', 'milk'],
                'recipes': ['French Toast', 'Bread Pudding', 'Milk Toast'],
                'description': 'Transform stale bread into delicious meals'
            },
            'pasta_cheese': {
                'ingredients': ['pasta', 'cheese'],
                'recipes': ['Mac and Cheese', 'Pasta Bake', 'Cheesy Pasta'],
                'description': 'Quick pasta solutions'
            }
        }

        leftover_solutions = {
            'top_leftover_ingredients': [{'name': ing, 'usage_count': count} for ing, count in top_leftovers],
            'common_combinations': leftover_combinations
        }

        # User-specific analytics (only if authenticated)
        user_specific = {}
        if user_id and mongo and ObjectId:
            try:
                user = mongo.db.users.find_one({'_id': ObjectId(user_id)})
                if user:
                    dashboard_data = user.get('dashboard_data', {})
                    analytics = user.get('analytics', {})

                    # User's most used ingredients
                    user_ingredients = dashboard_data.get('search_stats', {}).get('most_used_ingredients', {})
                    top_user_ingredients = sorted(user_ingredients.items(), key=lambda x: x[1], reverse=True)[:5]

                    # Recent search patterns
                    recent_searches = dashboard_data.get('recent_searches', [])

                    # Personalized leftover suggestions based on user history
                    user_leftover_suggestions = []
                    for ingredient, count in top_user_ingredients:
                        # Find recipes that use this ingredient
                        if recommender and recommender.recipes:
                            matching_recipes = [
                                r for r in recommender.recipes[:100]  # Limit search
                                if ingredient.lower() in [ing.lower() for ing in r.get('ingredients', [])]
                            ][:3]  # Top 3 matches

                            if matching_recipes:
                                user_leftover_suggestions.append({
                                    'ingredient': ingredient,
                                    'usage_count': count,
                                    'suggested_recipes': [
                                        {
                                            'id': r['id'],
                                            'name': r['name'],
                                            'ingredients': r['ingredients'][:4]  # First 4 ingredients
                                        } for r in matching_recipes
                                    ]
                                })

                    user_specific = {
                        'most_used_ingredients': [{'name': ing, 'count': count} for ing, count in top_user_ingredients],
                        'total_searches': dashboard_data.get('search_stats', {}).get('total_searches', 0),
                        'recent_search_count': len(recent_searches),
                        'personalized_leftover_suggestions': user_leftover_suggestions,
                        'cooking_streak': analytics.get('cooking_streak', {}),
                        'total_recipe_saves': analytics.get('total_recipe_saves', 0)
                    }
            except Exception as e:
                print(f"Error getting user-specific analytics: {e}")
                user_specific = {}

        return jsonify({
            'status': 'success',
            'data': {
                'popular_recipes': popular_recipes,
                'leftover_solutions': leftover_solutions,
                'user_specific': user_specific
            }
        })

    except Exception as e:
        print(f"Error getting prescriptive analytics: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error getting prescriptive analytics: {str(e)}'
        }), 500

@main_bp.route('/api/analytics/leftover-ingredients', methods=['GET'])
def get_leftover_ingredients_analytics():
    """
    Get analytics for most searched leftover-prone ingredients.

    Returns:
    --------
    {
        "status": "success",
        "data": {
            "most_searched_leftovers": [
                {"name": "chicken", "count": 245, "percentage": 18.5},
                {"name": "rice", "count": 189, "percentage": 14.2},
                ...
            ],
            "total_searches": 1324,
            "last_updated": "2024-01-01T12:00:00Z"
        }
    }
    """
    try:
        from api.models.user import mongo
        from collections import defaultdict
        from datetime import datetime

        # Define perishable ingredients that commonly become leftovers
        leftover_prone_ingredients = {
            # Fresh vegetables
            'tomato', 'tomatoes', 'lettuce', 'spinach', 'carrot', 'carrots',
            'broccoli', 'cauliflower', 'bell pepper', 'peppers', 'onion', 'onions',
            'celery', 'cucumber', 'zucchini', 'eggplant', 'cabbage', 'kale',
            'mushroom', 'mushrooms', 'potato', 'potatoes', 'sweet potato',

            # Fresh fruits
            'banana', 'bananas', 'apple', 'apples', 'orange', 'oranges',
            'berries', 'strawberry', 'strawberries', 'blueberry', 'blueberries',
            'grapes', 'lemon', 'lemons', 'lime', 'limes', 'avocado', 'avocados',

            # Dairy products
            'milk', 'cheese', 'yogurt', 'yoghurt', 'cream', 'butter',
            'sour cream', 'cottage cheese', 'mozzarella', 'cheddar',

            # Meat and protein
            'chicken', 'beef', 'pork', 'fish', 'salmon', 'tuna', 'shrimp',
            'turkey', 'lamb', 'bacon', 'ham', 'sausage', 'ground beef',
            'chicken breast', 'chicken thigh',

            # Fresh herbs
            'basil', 'cilantro', 'parsley', 'mint', 'dill', 'chives',
            'rosemary', 'thyme', 'oregano', 'sage',

            # Cooked grains and leftovers
            'rice', 'pasta', 'bread', 'noodles', 'quinoa', 'couscous',
            'leftover rice', 'leftover pasta', 'leftover chicken'
        }

        # Get all users' search data
        all_users = []
        if mongo:
            try:
                all_users = list(mongo.db.users.find({}, {
                    'dashboard_data.search_stats.most_used_ingredients': 1,
                    'dashboard_data.ingredient_history': 1,
                    'dashboard_data.recent_searches': 1
                }))
            except Exception as e:
                print(f"Warning: Could not fetch user data: {e}")
                all_users = []

        # Aggregate ingredient usage across all users
        global_ingredient_usage = defaultdict(int)
        total_ingredient_searches = 0

        for user in all_users:
            dashboard_data = user.get('dashboard_data', {})
            search_stats = dashboard_data.get('search_stats', {})
            most_used = search_stats.get('most_used_ingredients', {})

            for ingredient, count in most_used.items():
                # Normalize ingredient name (lowercase, strip whitespace)
                normalized_ingredient = ingredient.lower().strip()

                # Check if this ingredient is leftover-prone
                if normalized_ingredient in leftover_prone_ingredients:
                    global_ingredient_usage[normalized_ingredient] += count
                    total_ingredient_searches += count

        # Get top leftover ingredients
        top_leftovers = sorted(global_ingredient_usage.items(), key=lambda x: x[1], reverse=True)[:5]

        # Calculate percentages
        most_searched_leftovers = []
        for ingredient, count in top_leftovers:
            percentage = (count / total_ingredient_searches * 100) if total_ingredient_searches > 0 else 0
            most_searched_leftovers.append({
                'name': ingredient.title(),  # Capitalize for display
                'count': count,
                'percentage': round(percentage, 1)
            })

        # If no real data, provide fallback data
        if not most_searched_leftovers:
            most_searched_leftovers = [
                {'name': 'Chicken', 'count': 245, 'percentage': 22.1},
                {'name': 'Rice', 'count': 189, 'percentage': 17.0},
                {'name': 'Tomatoes', 'count': 167, 'percentage': 15.1},
                {'name': 'Onions', 'count': 134, 'percentage': 12.1},
                {'name': 'Carrots', 'count': 112, 'percentage': 10.1}
            ]
            total_ingredient_searches = sum(item['count'] for item in most_searched_leftovers)

        return jsonify({
            'status': 'success',
            'data': {
                'most_searched_leftovers': most_searched_leftovers,
                'total_searches': total_ingredient_searches,
                'last_updated': datetime.utcnow().isoformat()
            }
        })

    except Exception as e:
        print(f"Error getting leftover ingredients analytics: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Error getting leftover ingredients analytics: {str(e)}'
        }), 500

@main_bp.route('/api/analytics/track', methods=['POST'])
@jwt_required()
def track_user_event():
    """
    Track a user event for analytics.

    Request Body:
    ------------
    {
        "event_type": "recipe_view|recipe_save|review_given|search",
        "event_data": {
            "recipe_id": "123",
            "cuisine": "italian",
            "ingredients": ["chicken", "rice"]
        }
    }
    """
    try:
        # Get user ID from JWT
        user_id = get_jwt_identity()

        # Get request data
        data = request.get_json()

        if not data:
            return jsonify({
                'status': 'error',
                'message': 'No data provided'
            }), 400

        event_type = data.get('event_type')
        event_data = data.get('event_data', {})

        if not event_type:
            return jsonify({
                'status': 'error',
                'message': 'event_type is required'
            }), 400

        # Valid event types
        valid_events = ['recipe_view', 'recipe_save', 'review_given', 'search']
        if event_type not in valid_events:
            return jsonify({
                'status': 'error',
                'message': f'Invalid event_type. Must be one of: {valid_events}'
            }), 400

        # Debug logging
        print(f"🔍 DEBUG: Tracking event for user {user_id}")
        print(f"🔍 DEBUG: Event type: {event_type}")
        print(f"🔍 DEBUG: Event data: {event_data}")

        # Update user analytics
        success = update_user_analytics(user_id, event_type, event_data)

        print(f"🔍 DEBUG: Analytics update success: {success}")

        if success:
            return jsonify({
                'status': 'success',
                'message': 'Event tracked successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to track event'
            }), 500

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error tracking event: {str(e)}'
        }), 500

# ==================== ADMIN API ROUTES ====================

@main_bp.route('/api/admin/users', methods=['GET'])
@jwt_required()
def get_all_users_admin():
    """
    Get all users with their complete information (admin only).

    Query Parameters:
    ----------------
    page : int, optional
        Page number for pagination (default: 1)
    limit : int, optional
        Number of users per page (default: 20)
    search : str, optional
        Search by name or email
    """
    try:
        # Check if user is admin
        from flask_jwt_extended import get_jwt
        claims = get_jwt()
        if not claims.get('is_admin', False):
            return jsonify({
                'status': 'error',
                'message': 'Admin privileges required'
            }), 403

        # Get query parameters
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        search = request.args.get('search', '').strip()

        # Calculate skip value for pagination
        skip = (page - 1) * limit

        # Build query
        query = {}
        if search:
            query = {
                '$or': [
                    {'name': {'$regex': search, '$options': 'i'}},
                    {'email': {'$regex': search, '$options': 'i'}}
                ]
            }

        # Get users from database
        from api.models.user import mongo

        # Get total count
        total_users = mongo.db.users.count_documents(query)

        # Get users with pagination
        users_cursor = mongo.db.users.find(query).skip(skip).limit(limit).sort('created_at', -1)
        users = list(users_cursor)

        # Format user data (remove sensitive password field)
        formatted_users = []
        for user in users:
            user_data = {
                'id': str(user['_id']),
                'name': user['name'],
                'email': user['email'],
                'created_at': user.get('created_at'),
                'updated_at': user.get('updated_at'),
                'profile_image': user.get('profile_image'),
                'preferences': user.get('preferences', {}),
                'analytics': user.get('analytics', {}),
                'dashboard_data': user.get('dashboard_data', {}),
                'is_admin': user.get('is_admin', False)
            }
            formatted_users.append(user_data)

        return jsonify({
            'status': 'success',
            'users': formatted_users,
            'pagination': {
                'current_page': page,
                'total_pages': (total_users + limit - 1) // limit,
                'total_users': total_users,
                'users_per_page': limit
            }
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error fetching users: {str(e)}'
        }), 500

@main_bp.route('/api/admin/user/<user_id>/details', methods=['GET'])
@jwt_required()
def get_user_details_admin(user_id):
    """
    Get detailed information for a specific user (admin only).
    Includes saved recipes, reviews, verifications, etc.
    """
    try:
        # Check if user is admin
        from flask_jwt_extended import get_jwt
        claims = get_jwt()
        if not claims.get('is_admin', False):
            return jsonify({
                'status': 'error',
                'message': 'Admin privileges required'
            }), 403

        from api.models.user import mongo, get_user_by_id
        from api.models.recipe import get_saved_recipes_for_user

        # Get user basic info
        user = get_user_by_id(user_id)
        if not user:
            return jsonify({
                'status': 'error',
                'message': 'User not found'
            }), 404

        # Get user's saved recipes
        saved_recipes = get_saved_recipes_for_user(user_id)

        # Get user's reviews
        reviews = list(mongo.db.recipe_reviews.find({'user_id': user_id}).sort('created_at', -1))

        # Get user's verifications
        verifications = list(mongo.db.recipe_verifications.find({'user_id': user_id}).sort('created_at', -1))

        # Get user's review votes
        review_votes = list(mongo.db.review_votes.find({'user_id': user_id}).sort('created_at', -1))

        # Format the data
        user_details = {
            'basic_info': {
                'id': str(user['_id']),
                'name': user['name'],
                'email': user['email'],
                'created_at': user.get('created_at'),
                'updated_at': user.get('updated_at'),
                'profile_image': user.get('profile_image'),
                'is_admin': user.get('is_admin', False)
            },
            'preferences': user.get('preferences', {}),
            'analytics': user.get('analytics', {}),
            'dashboard_data': user.get('dashboard_data', {}),
            'saved_recipes': {
                'count': len(saved_recipes),
                'recipes': [
                    {
                        'id': str(recipe['_id']),
                        'name': recipe['name'],
                        'saved_at': recipe.get('created_at'),
                        'ingredients_count': len(recipe.get('ingredients', []))
                    } for recipe in saved_recipes
                ]
            },
            'reviews': {
                'count': len(reviews),
                'reviews': [
                    {
                        'id': str(review['_id']),
                        'recipe_id': review['recipe_id'],
                        'rating': review['rating'],
                        'review_text': review.get('review_text'),
                        'helpful_votes': review.get('helpful_votes', 0),
                        'unhelpful_votes': review.get('unhelpful_votes', 0),
                        'created_at': review.get('created_at'),
                        'updated_at': review.get('updated_at')
                    } for review in reviews
                ]
            },
            'verifications': {
                'count': len(verifications),
                'verifications': [
                    {
                        'id': str(verification['_id']),
                        'recipe_id': verification['recipe_id'],
                        'notes': verification.get('notes'),
                        'has_photo': 'photo_data' in verification,
                        'created_at': verification.get('created_at')
                    } for verification in verifications
                ]
            },
            'review_votes': {
                'count': len(review_votes),
                'votes': [
                    {
                        'id': str(vote['_id']),
                        'review_id': vote['review_id'],
                        'vote_type': vote['vote_type'],
                        'created_at': vote.get('created_at')
                    } for vote in review_votes
                ]
            }
        }

        return jsonify({
            'status': 'success',
            'user': user_details
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error fetching user details: {str(e)}'
        }), 500

@main_bp.route('/api/admin/stats', methods=['GET'])
@jwt_required()
def get_admin_stats():
    """
    Get overall system statistics (admin only).
    """
    try:
        # Check if user is admin
        from flask_jwt_extended import get_jwt
        claims = get_jwt()
        if not claims.get('is_admin', False):
            return jsonify({
                'status': 'error',
                'message': 'Admin privileges required'
            }), 403

        from api.models.user import mongo

        # Get various statistics
        total_users = mongo.db.users.count_documents({})
        total_reviews = mongo.db.recipe_reviews.count_documents({})
        total_verifications = mongo.db.recipe_verifications.count_documents({})
        total_saved_recipes = mongo.db.saved_recipes.count_documents({})
        total_review_votes = mongo.db.review_votes.count_documents({})

        # Get recent activity (last 30 days)
        from datetime import datetime, timedelta
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)

        recent_users = mongo.db.users.count_documents({'created_at': {'$gte': thirty_days_ago}})
        recent_reviews = mongo.db.recipe_reviews.count_documents({'created_at': {'$gte': thirty_days_ago}})
        recent_verifications = mongo.db.recipe_verifications.count_documents({'created_at': {'$gte': thirty_days_ago}})

        # Get top reviewers
        top_reviewers = list(mongo.db.recipe_reviews.aggregate([
            {'$group': {'_id': '$user_id', 'review_count': {'$sum': 1}, 'user_name': {'$first': '$user_name'}}},
            {'$sort': {'review_count': -1}},
            {'$limit': 10}
        ]))

        # Get rating distribution
        rating_distribution = list(mongo.db.recipe_reviews.aggregate([
            {'$group': {'_id': '$rating', 'count': {'$sum': 1}}},
            {'$sort': {'_id': 1}}
        ]))

        stats = {
            'overview': {
                'total_users': total_users,
                'total_reviews': total_reviews,
                'total_verifications': total_verifications,
                'total_saved_recipes': total_saved_recipes,
                'total_review_votes': total_review_votes
            },
            'recent_activity': {
                'new_users_30_days': recent_users,
                'new_reviews_30_days': recent_reviews,
                'new_verifications_30_days': recent_verifications
            },
            'top_reviewers': [
                {
                    'user_id': reviewer['_id'],
                    'user_name': reviewer['user_name'],
                    'review_count': reviewer['review_count']
                } for reviewer in top_reviewers
            ],
            'rating_distribution': {
                str(rating['_id']): rating['count'] for rating in rating_distribution
            }
        }

        return jsonify({
            'status': 'success',
            'stats': stats
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error fetching admin stats: {str(e)}'
        }), 500

# ==================== DEVELOPER DEBUG API ROUTES ====================

@main_bp.route('/api/dev/users', methods=['GET'])
def get_all_users_dev():
    """
    Developer endpoint to get all users with basic info.
    WARNING: This is for development only - remove in production!
    """
    try:
        from api.models.user import mongo

        # Get all users
        users = list(mongo.db.users.find({}, {
            'name': 1,
            'email': 1,
            'created_at': 1,
            'analytics.total_reviews_given': 1,
            'analytics.total_recipe_saves': 1
        }).sort('created_at', -1))

        # Format user data
        formatted_users = []
        for user in users:
            user_data = {
                'id': str(user['_id']),
                'name': user['name'],
                'email': user['email'],
                'created_at': user.get('created_at'),
                'total_reviews': user.get('analytics', {}).get('total_reviews_given', 0),
                'total_saves': user.get('analytics', {}).get('total_recipe_saves', 0)
            }
            formatted_users.append(user_data)

        return jsonify({
            'status': 'success',
            'count': len(formatted_users),
            'users': formatted_users
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error fetching users: {str(e)}'
        }), 500

@main_bp.route('/api/dev/user/<user_identifier>', methods=['GET'])
def get_user_complete_data_dev(user_identifier):
    """
    Developer endpoint to get complete user data by email or ID.
    WARNING: This is for development only - remove in production!
    """
    try:
        from api.models.user import mongo, get_user_by_id, get_user_by_email
        from api.models.recipe import get_saved_recipes_for_user
        from bson.objectid import ObjectId

        # Try to find user by email first, then by ID
        user = None
        if '@' in user_identifier:
            user = get_user_by_email(user_identifier)
        else:
            user = get_user_by_id(user_identifier)

        if not user:
            return jsonify({
                'status': 'error',
                'message': 'User not found'
            }), 404

        user_id = str(user['_id'])

        # Get user's saved recipes
        saved_recipes = get_saved_recipes_for_user(user_id)

        # Get user's reviews
        reviews = list(mongo.db.recipe_reviews.find({'user_id': user_id}).sort('created_at', -1))

        # Get user's verifications
        verifications = list(mongo.db.recipe_verifications.find({'user_id': user_id}).sort('created_at', -1))

        # Get user's review votes
        review_votes = list(mongo.db.review_votes.find({'user_id': user_id}).sort('created_at', -1))

        # Prepare complete user data
        complete_data = {
            'basic_info': {
                'id': user_id,
                'name': user['name'],
                'email': user['email'],
                'password_hash': user.get('password', b'').decode('utf-8', errors='ignore') if isinstance(user.get('password'), bytes) else str(user.get('password', '')),
                'created_at': user.get('created_at'),
                'updated_at': user.get('updated_at'),
                'profile_image': user.get('profile_image'),
                'is_admin': user.get('is_admin', False)
            },
            'preferences': user.get('preferences', {}),
            'analytics': user.get('analytics', {}),
            'dashboard_data': user.get('dashboard_data', {}),
            'saved_recipes': {
                'count': len(saved_recipes),
                'recipes': [
                    {
                        'id': str(recipe['_id']),
                        'name': recipe['name'],
                        'ingredients': recipe.get('ingredients', []),
                        'steps': recipe.get('steps', []),
                        'techniques': recipe.get('techniques', []),
                        'calorie_level': recipe.get('calorie_level'),
                        'saved_at': recipe.get('created_at')
                    } for recipe in saved_recipes
                ]
            },
            'reviews': {
                'count': len(reviews),
                'reviews': [
                    {
                        'id': str(review['_id']),
                        'recipe_id': review['recipe_id'],
                        'rating': review['rating'],
                        'review_text': review.get('review_text'),
                        'helpful_votes': review.get('helpful_votes', 0),
                        'unhelpful_votes': review.get('unhelpful_votes', 0),
                        'created_at': review.get('created_at'),
                        'updated_at': review.get('updated_at')
                    } for review in reviews
                ]
            },
            'verifications': {
                'count': len(verifications),
                'verifications': [
                    {
                        'id': str(verification['_id']),
                        'recipe_id': verification['recipe_id'],
                        'notes': verification.get('notes'),
                        'has_photo': 'photo_data' in verification,
                        'photo_filename': verification.get('photo_filename'),
                        'created_at': verification.get('created_at')
                    } for verification in verifications
                ]
            },
            'review_votes': {
                'count': len(review_votes),
                'votes': [
                    {
                        'id': str(vote['_id']),
                        'review_id': vote['review_id'],
                        'vote_type': vote['vote_type'],
                        'created_at': vote.get('created_at')
                    } for vote in review_votes
                ]
            }
        }

        return jsonify({
            'status': 'success',
            'user': complete_data
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({
            'status': 'error',
            'message': f'Error fetching user data: {str(e)}'
        }), 500

# ==================== COMMUNITY FEATURES API ROUTES ====================

@main_bp.route('/api/recipe/<recipe_id>/review', methods=['POST'])
@jwt_required()
def add_recipe_review_api(recipe_id):
    """
    Add or update a review for a recipe.

    Request Body:
    ------------
    {
        "rating": 5,
        "review_text": "Great recipe! Easy to follow and delicious."
    }
    """
    import time
    start_time = time.time()

    try:
        # Get user ID from JWT
        user_id = get_jwt_identity()
        print(f"DEBUG: Review submission started - user_id={user_id}, recipe_id={recipe_id}")

        # Validate user_id
        if not user_id:
            print("ERROR: No user_id found in JWT token")
            return jsonify({
                'status': 'error',
                'message': 'Invalid authentication token'
            }), 401

        # Get request data
        data = request.get_json()
        print(f"DEBUG: Request data received: {data}")

        if not data:
            print("ERROR: No data provided in request")
            return jsonify({
                'status': 'error',
                'message': 'No data provided'
            }), 400

        # Validate required fields
        if 'rating' not in data:
            print("ERROR: Rating field missing from request")
            return jsonify({
                'status': 'error',
                'message': 'Rating is required'
            }), 400

        rating = data.get('rating')
        # Safely handle review_text - it might be None, empty string, or actual text
        review_text_raw = data.get('review_text')
        if review_text_raw is None:
            review_text = ''
        else:
            review_text = str(review_text_raw).strip()
        print(f"DEBUG: Parsed data - recipe_id={recipe_id}, rating={rating}, review_text='{review_text}'")

        # Validate rating
        try:
            rating = int(rating)
            if rating < 1 or rating > 5:
                raise ValueError()
        except (ValueError, TypeError):
            print(f"ERROR: Invalid rating value: {rating}")
            return jsonify({
                'status': 'error',
                'message': 'Rating must be an integer between 1 and 5'
            }), 400

        print(f"DEBUG: About to call add_recipe_review with user_id={user_id}, recipe_id={recipe_id}, rating={rating}")

        # Check database connection before proceeding
        try:
            from api.models.user import mongo
            # Test database connection
            mongo.db.command('ping')
            print("DEBUG: Database connection verified")
        except Exception as db_err:
            print(f"ERROR: Database connection failed: {db_err}")
            return jsonify({
                'status': 'error',
                'message': 'Database connection error. Please try again.'
            }), 503

        # Add the review
        result = add_recipe_review(user_id, recipe_id, rating, review_text if review_text else None)
        print(f"DEBUG: add_recipe_review result: {result}")

        # Update hybrid recommender with new rating
        recommender = getattr(current_app, 'recommender', None)
        if result['status'] == 'success' and recommender:
            try:
                recommender.update_user_preference(user_id, recipe_id, rating)
                print(f"DEBUG: Updated hybrid recommender with rating: user={user_id}, recipe={recipe_id}, rating={rating}")
            except Exception as e:
                print(f"WARNING: Could not update hybrid recommender: {e}")

        # Track analytics for review
        if result['status'] == 'success':
            try:
                update_user_analytics(user_id, 'review_given', {
                    'recipe_id': recipe_id,
                    'rating': rating,
                    'has_review_text': bool(review_text)
                })
                print(f"DEBUG: Analytics tracked for review: user={user_id}, recipe={recipe_id}")
            except Exception as e:
                print(f"WARNING: Could not track review analytics: {e}")

        processing_time = time.time() - start_time
        print(f"DEBUG: Review submission completed in {processing_time:.2f} seconds")

        if result['status'] == 'success':
            return jsonify(result)
        else:
            print(f"ERROR: Review submission failed: {result}")
            return jsonify(result), 400

    except Exception as e:
        processing_time = time.time() - start_time
        print(f"ERROR: Exception in add_recipe_review_api after {processing_time:.2f} seconds: {str(e)}")
        import traceback
        traceback.print_exc()

        return jsonify({
            'status': 'error',
            'message': f'Error adding review: {str(e)}'
        }), 500

@main_bp.route('/api/recipe/<recipe_id>/reviews', methods=['GET'])
def get_recipe_reviews_api(recipe_id):
    """
    Get reviews for a recipe.

    Query Parameters:
    ----------------
    sort_by : str, optional
        Sort criteria ('helpful', 'recent', 'rating_high', 'rating_low')
    limit : int, optional
        Maximum number of reviews to return (default: 50)
    skip : int, optional
        Number of reviews to skip for pagination (default: 0)
    """
    try:
        # Get query parameters
        sort_by = request.args.get('sort_by', 'helpful')
        limit = int(request.args.get('limit', 50))
        skip = int(request.args.get('skip', 0))

        # Get reviews
        result = get_recipe_reviews(recipe_id, sort_by, limit, skip)

        if result['status'] == 'success':
            return jsonify(result)
        else:
            return jsonify(result), 400

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error fetching reviews: {str(e)}'
        }), 500

@main_bp.route('/api/recipe/<recipe_id>/rating-summary', methods=['GET'])
def get_recipe_rating_summary_api(recipe_id):
    """
    Get rating summary for a recipe.
    """
    try:
        result = get_recipe_rating_summary(recipe_id)

        if result['status'] == 'success':
            return jsonify(result)
        else:
            return jsonify(result), 400

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error fetching rating summary: {str(e)}'
        }), 500

@main_bp.route('/api/review/<review_id>/vote', methods=['POST'])
@jwt_required()
def vote_on_review_api(review_id):
    """
    Vote on a review as helpful or unhelpful.

    Request Body:
    ------------
    {
        "vote_type": "helpful"  // or "unhelpful"
    }
    """
    try:
        # Get user ID from JWT
        user_id = get_jwt_identity()

        # Get request data
        data = request.get_json()

        if not data or 'vote_type' not in data:
            return jsonify({
                'status': 'error',
                'message': 'vote_type is required'
            }), 400

        vote_type = data.get('vote_type')

        if vote_type not in ['helpful', 'unhelpful']:
            return jsonify({
                'status': 'error',
                'message': 'vote_type must be "helpful" or "unhelpful"'
            }), 400

        # Vote on review
        result = vote_on_review(user_id, review_id, vote_type)

        if result['status'] == 'success':
            return jsonify(result)
        else:
            return jsonify(result), 400

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error voting on review: {str(e)}'
        }), 500

@main_bp.route('/api/recipe/<recipe_id>/verify', methods=['POST'])
@jwt_required()
def add_recipe_verification_api(recipe_id):
    """
    Add a recipe verification (mark as tried and tested).

    Request Body:
    ------------
    {
        "notes": "Turned out great! Added extra garlic.",
        "photo": "data:image/jpeg;base64,..."  // optional base64 encoded image
    }
    """
    import time
    start_time = time.time()

    try:
        # Get user ID from JWT
        user_id = get_jwt_identity()
        print(f"DEBUG: Verification submission started - user_id={user_id}, recipe_id={recipe_id}")

        # Validate user_id
        if not user_id:
            print("ERROR: No user_id found in JWT token")
            return jsonify({
                'status': 'error',
                'message': 'Invalid authentication token'
            }), 401

        # Get request data
        data = request.get_json()
        print(f"DEBUG: Verification request data: {data}")

        # Safely handle notes - it might be None, empty string, or actual text
        notes_raw = data.get('notes') if data else None
        if notes_raw is None:
            notes = ''
        else:
            notes = str(notes_raw).strip()
        photo_data = data.get('photo') if data else None

        # Check database connection before proceeding
        try:
            from api.models.user import mongo
            # Test database connection
            mongo.db.command('ping')
            print("DEBUG: Database connection verified for verification")
        except Exception as db_err:
            print(f"ERROR: Database connection failed for verification: {db_err}")
            return jsonify({
                'status': 'error',
                'message': 'Database connection error. Please try again.'
            }), 503

        # Add the verification
        result = add_recipe_verification(user_id, recipe_id, photo_data, notes if notes else None)
        print(f"DEBUG: add_recipe_verification result: {result}")

        processing_time = time.time() - start_time
        print(f"DEBUG: Verification submission completed in {processing_time:.2f} seconds")

        if result['status'] == 'success':
            return jsonify(result)
        else:
            print(f"ERROR: Verification submission failed: {result}")
            return jsonify(result), 400

    except Exception as e:
        processing_time = time.time() - start_time
        print(f"ERROR: Exception in add_recipe_verification_api after {processing_time:.2f} seconds: {str(e)}")
        import traceback
        traceback.print_exc()

        return jsonify({
            'status': 'error',
            'message': f'Error adding verification: {str(e)}'
        }), 500

@main_bp.route('/api/recipe/<recipe_id>/verifications', methods=['GET'])
def get_recipe_verifications_api(recipe_id):
    """
    Get verifications for a recipe.

    Query Parameters:
    ----------------
    limit : int, optional
        Maximum number of verifications to return (default: 20)
    skip : int, optional
        Number of verifications to skip for pagination (default: 0)
    """
    try:
        # Get query parameters
        limit = int(request.args.get('limit', 20))
        skip = int(request.args.get('skip', 0))

        # Get verifications
        result = get_recipe_verifications(recipe_id, limit, skip)

        if result['status'] == 'success':
            return jsonify(result)
        else:
            return jsonify(result), 400

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error fetching verifications: {str(e)}'
        }), 500

@main_bp.route('/api/verification/<verification_id>/photo', methods=['GET'])
def get_verification_photo_api(verification_id):
    """
    Get photo for a verification.
    """
    try:
        result = get_verification_photo(verification_id)

        if result['status'] == 'success':
            return jsonify(result)
        else:
            return jsonify(result), 404 if 'not found' in result['message'].lower() else 400

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error fetching verification photo: {str(e)}'
        }), 500

@main_bp.route('/api/recipe/<recipe_id>/user-review', methods=['GET'])
@jwt_required()
def get_user_review_for_recipe_api(recipe_id):
    """
    Get the current user's review for a specific recipe.
    """
    try:
        # Get user ID from JWT
        user_id = get_jwt_identity()

        result = get_user_review_for_recipe(user_id, recipe_id)

        if result['status'] == 'success':
            return jsonify(result)
        else:
            return jsonify(result), 400

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error fetching user review: {str(e)}'
        }), 500

@main_bp.route('/api/recipe/<recipe_id>/user-verification', methods=['GET'])
@jwt_required()
def get_user_verification_for_recipe_api(recipe_id):
    """
    Get the current user's verification for a specific recipe.
    """
    try:
        # Get user ID from JWT
        user_id = get_jwt_identity()

        result = get_user_verification_for_recipe(user_id, recipe_id)

        if result['status'] == 'success':
            return jsonify(result)
        else:
            return jsonify(result), 400

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error fetching user verification: {str(e)}'
        }), 500
